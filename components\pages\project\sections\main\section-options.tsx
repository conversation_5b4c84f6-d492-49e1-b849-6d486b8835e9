"use client";

import React from "react";
import { createSectionAction } from "@/actions/section";
import { $Enums, Section } from "@prisma/client";
import { toast } from "sonner";
import { Card, CardContent } from "@/components/ui/card";
import { options } from "@/lib/static";

type Props = {
  index: Section["index"];
  projectId: Section["projectId"];
};

const SectionOptions = ({ index, projectId }: Props) => {
  const handleSectionCreate = async (type: $Enums.SectionType) => {
    const { data, message, error } = await createSectionAction({
      projectId: projectId,
      index: index,
      type,
    });

    if (data) {
      toast.success(message);
    }

    if (error) {
      toast.error(message);
    }
  };

  return (
    <div className="py-2">
      <div className="grid grid-cols-1 sm:grid-cols-3 lg:grid-cols-5 gap-4 overflow-hidden">
        {options.map((option) => (
          <Card
            key={option.type}
            className="group cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-105 border-border/50 hover:border-border"
            onClick={() => handleSectionCreate(option.type)}
          >
            <CardContent className="p-2 text-center">
              {/* Icon with gradient background */}
              <div
                className={`w-8 h-8 mx-auto mb-3 bg-gradient-to-br ${option.bgColor} rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-200`}
              >
                <option.icon
                  className={`w-6 h-6 ${option.iconColor} group-hover:scale-110 transition-all duration-200`}
                />
              </div>

              {/* Label */}
              <h3 className="font-semibold text-sm text-foreground mb-1 group-hover:text-primary transition-colors">
                {option.label}
              </h3>

              {/* Description */}
              <p className="text-xs text-muted-foreground leading-relaxed">
                {option.description}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default SectionOptions;
