import { $Enums } from "@prisma/client";
import {
  Image,
  LetterText,
  MapPin,
  ShoppingCart,
  Users,
  LayoutList,
  Video,
  ClipboardList,
} from "lucide-react";

export const options = [
  {
    id: 1,
    type: $Enums.SectionType.TEXT,
    icon: LetterText,
    label: "Text",
    description: "Add headings, paragraphs, and rich text content",
    iconColor: "text-blue-600 dark:text-blue-400",
    bgColor:
      "from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20",
  },
  {
    id: 2,
    type: $Enums.SectionType.IMAGE,
    icon: Image,
    label: "Image",
    description: "Display beautiful images and galleries",
    iconColor: "text-green-600 dark:text-green-400",
    bgColor:
      "from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20",
  },
  {
    id: 3,
    type: $Enums.SectionType.VIDEO,
    icon: Video,
    label: "Video",
    description: "Embed videos and multimedia content",
    iconColor: "text-purple-600 dark:text-purple-400",
    bgColor:
      "from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20",
  },
  {
    id: 4,
    type: $Enums.SectionType.TEXTIMAGE,
    icon: LayoutList,
    label: "Text + Image",
    description: "Combine text with images for rich layouts",
    iconColor: "text-orange-600 dark:text-orange-400",
    bgColor:
      "from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20",
  },
  {
    id: 5,
    type: $Enums.SectionType.FORM,
    icon: ClipboardList,
    label: "Form",
    description: "Contact forms and data collection",
    iconColor: "text-pink-600 dark:text-pink-400",
    bgColor:
      "from-pink-50 to-pink-100 dark:from-pink-900/20 dark:to-pink-800/20",
  },
  {
    id: 6,
    type: $Enums.SectionType.SOCIAL,
    icon: Users,
    label: "Social",
    description: "Social media feeds and sharing buttons",
    iconColor: "text-indigo-600 dark:text-indigo-400",
    bgColor:
      "from-indigo-50 to-indigo-100 dark:from-indigo-900/20 dark:to-indigo-800/20",
  },
  {
    id: 7,
    type: $Enums.SectionType.MAP,
    icon: MapPin,
    label: "Map",
    description: "Interactive maps and location displays",
    iconColor: "text-teal-600 dark:text-teal-400",
    bgColor:
      "from-teal-50 to-teal-100 dark:from-teal-900/20 dark:to-teal-800/20",
  },
  {
    id: 8,
    type: $Enums.SectionType.ECOMMERCE,
    icon: ShoppingCart,
    label: "E-commerce",
    description: "Product showcases and shopping features",
    iconColor: "text-red-600 dark:text-red-400",
    bgColor: "from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20",
  },
];
